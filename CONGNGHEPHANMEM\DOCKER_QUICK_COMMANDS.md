# ⚡ **DOCKER QUICK COMMANDS**

## 🚀 **SETUP (1 LẦN DUY NHẤT)**
```bash
cd CONGNGHEPHANMEM
copy .env.example .env
```

## 🔥 **LỆNH CHÍNH (DÙNG HÀNG NGÀY)**

### **▶️ CHẠY**
```bash
docker-compose up -d                    # Ch<PERSON>y tất cả
docker-compose up --build -d            # Build + chạy
```

### **⏹️ DỪNG**
```bash
docker-compose stop                     # Dừng
docker-compose down                     # Dừng + xóa containers
```

### **🔄 RESTART**
```bash
docker-compose restart                  # Restart tất cả
docker-compose restart backend          # Restart backend
```

### **📊 KIỂM TRA**
```bash
docker-compose ps                       # Trạng thái containers
docker-compose logs -f                  # Xem logs
curl http://localhost:3000/api/health   # Test API
```

## 🛠️ **XỬ LÝ LỖI**

### **❌ Khi có lỗi**
```bash
docker-compose logs backend             # Xem lỗi
docker-compose restart backend          # Restart
```

### **🔄 Reset hoàn toàn**
```bash
docker-compose down -v
docker-compose up --build -d
```

### **🧹 Dọn dẹp (khi cần)**
```bash
docker system prune -a
```

## 💾 **DATABASE**

### **🌱 Seed data**
```bash
docker-compose exec backend node scripts/seed-data.js
```

### **💾 Backup**
```bash
docker exec nafood-mongodb mongodump --uri="*******************************************************************" --out="/tmp/backup"
docker cp nafood-mongodb:/tmp/backup ./backup
```

## 🖥️ **TRUY CẬP**

### **🐚 Vào container**
```bash
docker-compose exec backend bash       # Backend shell
docker-compose exec mongodb mongosh    # MongoDB shell
```

## 📱 **CHEAT SHEET**

| **Cần** | **Lệnh** |
|---------|----------|
| **Chạy** | `docker-compose up -d` |
| **Dừng** | `docker-compose down` |
| **Logs** | `docker-compose logs -f` |
| **Status** | `docker-compose ps` |
| **Test** | `curl http://localhost:3000/api/health` |
| **Reset** | `docker-compose down -v && docker-compose up --build -d` |

## 🎯 **WORKFLOW**

### **🌅 Bắt đầu**
```bash
docker-compose up -d
```

### **🔍 Kiểm tra**
```bash
docker-compose ps
curl http://localhost:3000/api/health
```

### **🌙 Kết thúc**
```bash
docker-compose stop
```

## ✅ **THÀNH CÔNG KHI THẤY**
```json
{
  "status": "healthy",
  "timestamp": "2025-01-24T...",
  "mongodb": "connected"
}
```

## 🔗 **URLS**
- **API**: http://localhost:3000/api
- **Health**: http://localhost:3000/api/health
- **Products**: http://localhost:3000/api/products

---

**🚀 Copy & paste các lệnh trên để sử dụng Docker!**

**💡 Lưu ý**: Lệnh `docker-compose up --build -d` sẽ build lại images và chạy containers ở chế độ nền (detached mode).