# Sử dụng Node.js 18 Alpine làm base image (nhẹ và bảo mật)
FROM node:18-alpine

# Cài đặt các dependencies hệ thống cần thiết cho bcrypt và native modules
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    curl

# Thiết lập thư mục làm việc trong container
WORKDIR /app

# Copy package files trước để tận dụng Docker layer caching
COPY package*.json ./

# Cài đặt dependencies với error handling tốt hơn
RUN npm ci --no-audit --no-fund && \
    npm cache clean --force

# Copy source code của ứng dụng
COPY server ./server
COPY shared ./shared
COPY public ./public
COPY scripts ./scripts
COPY *.js ./
COPY tailwind.config.js ./
COPY postcss.config.js ./
COPY vite.config.js ./

# Tạo các thư mục cần thiết
RUN mkdir -p public/uploads && \
    mkdir -p logs

# Tạo user không phải root để tăng bảo mật
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nafood -u 1001

# Thay đổi quyền sở hữu thư mục app
RUN chown -R nafood:nodejs /app && \
    chmod -R 755 /app

# Chuyển sang user không phải root
USER nafood

# Thiết lập biến môi trường
ENV NODE_ENV=production
ENV DOCKER_ENV=true
ENV PORT=3000

# Mở port cho ứng dụng
EXPOSE 3000

# Health check nâng cao với timeout và retry tốt hơn
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
  CMD curl -f http://localhost:3000/api/health || exit 1

# Khởi động ứng dụng với signal handling phù hợp
CMD ["npm", "run", "start:prod"]
