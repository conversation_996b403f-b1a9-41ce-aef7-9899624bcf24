services:
  # Backend API - Server <PERSON>ng dụng ch<PERSON>h (sử dụng MongoDB Atlas)
  backend:
    build:
      context: ..  # Build từ thư mục gốc
      dockerfile: CONGNGHEPHANMEM/Dockerfile
    container_name: nafood-backend-atlas
    restart: unless-stopped
    environment:
      NODE_ENV: production
      DOCKER_ENV: "true"
      PORT: 3000
      # Kết nối MongoDB Atlas (cloud)
      MONGODB_URI: mongodb+srv://admin:<EMAIL>/nafood?retryWrites=true&w=majority
      # ⚠️ QUAN TRỌNG: Thay đổi secrets này trong production
      JWT_SECRET: your-super-secret-jwt-key-change-in-production
      SESSION_SECRET: your-session-secret-change-in-production
      FRONTEND_URL: http://localhost:3000
    ports:
      - "3000:3000"  # Port chính của ứng dụng
    volumes:
      - ./public/uploads:/app/public/uploads  # Lưu trữ file upload
      - ./logs:/app/logs  # Lưu trữ log files
    networks:
      - nafood-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 40s  # Thời gian chờ backend khởi động
    deploy:
      resources:
        limits:
          memory: 512M    # Giới hạn RAM cho backend
        reservations:
          memory: 256M    # RAM tối thiểu

  # Redis for Session Store (Tùy chọn - dùng cho cache và session)
  redis:
    image: redis:7-alpine
    container_name: nafood-redis-atlas
    restart: unless-stopped
    ports:
      - "6379:6379"  # Port Redis
    volumes:
      - redis_data:/data  # Lưu trữ Redis data persistent
    networks:
      - nafood-network
    command: redis-server --appendonly yes  # Bật persistence

# Định nghĩa volumes để lưu trữ dữ liệu persistent
volumes:
  redis_data:
    driver: local  # Lưu trữ dữ liệu Redis

# Định nghĩa network để các container giao tiếp với nhau
networks:
  nafood-network:
    driver: bridge  # Bridge network cho internal communication
